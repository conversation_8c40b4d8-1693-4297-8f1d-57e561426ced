<?php
/**
 * DANH SÁCH TẤT CẢ CÁC BIẾN CÓ SẴN TRONG CHECKOUT.BLADE.PHP
 * ========================================================
 * 
 * File này hiển thị tất cả các biến được truyền từ CourseController vào view checkout.blade.php
 * Bạn có thể sử dụng các biến này để tùy chỉnh giao diện checkout
 */

// =====================================================
// 1. THÔNG TIN KHÓA HỌC CHÍNH
// =====================================================
$course_details = [
    'id' => 'ID của khóa học',
    'title' => 'Tên khóa học',
    'slug' => 'Đường dẫn thân thiện',
    'description' => 'Mô tả khóa học',
    'price' => 'Giá gốc',
    'discounted_price' => 'Giá sau giảm',
    'discount_flag' => 'Cờ giảm giá (0/1)',
    'is_paid' => 'Khóa học có phí hay miễn phí',
    'thumbnail' => 'Ảnh đại diện khóa học',
    'user_id' => 'ID người tạo khóa học',
    'checkout_enabled' => 'Bật/tắt trang checkout',
    'checkout_heading_1' => 'Tiêu đề 1 trang checkout',
    'checkout_heading_2' => 'Tiêu đề 2 trang checkout', 
    'checkout_heading_3' => 'Tiêu đề 3 trang checkout',
    'checkout_heading_4' => 'Tiêu đề 4 trang checkout',
    'checkout_last_slots' => 'Số slot cuối cùng',
    'checkout_intro_video_link' => 'Link video giới thiệu',
    'checkout_exclusive_offer_image' => 'Ảnh ưu đãi độc quyền',
    'price_trends' => 'Xu hướng giá (JSON)',
    'expiry_period' => 'Thời hạn khóa học (tháng)',
    
    // Phương thức có sẵn
    'hasMultiplePricingPlans()' => 'Kiểm tra có nhiều gói giá không',
    'getPricingPlans()' => 'Lấy danh sách các gói giá',
    'getPopularPlan()' => 'Lấy gói phổ biến',
    'getPlanCurrentPrice($plan_id)' => 'Lấy giá hiện tại của gói',
    'getCheckoutOffersData()' => 'Lấy dữ liệu ưu đãi checkout'
];

// =====================================================
// 2. THÔNG TIN THANH TOÁN & NGÂN HÀNG
// =====================================================
$payment_gateway = [
    'identifier' => 'sepay',
    'keys' => 'JSON chứa thông tin ngân hàng'
];

$account_name = 'Tên chủ tài khoản ngân hàng';
$bank_code = 'Mã ngân hàng (VD: 970448)';
$account_number = 'Số tài khoản ngân hàng';

// =====================================================
// 3. THÔNG TIN ĐĂNG KÝ & ENROLLMENT
// =====================================================
$enroll_status = 'Trạng thái đăng ký (valid/invalid/expired)';
$enrollment_status = 'Trạng thái enrollment (true/false)';

// =====================================================
// 4. THÔNG TIN COUPON & GIẢM GIÁ
// =====================================================
$coupons = [
    'Mảng chứa các mã coupon có thể sử dụng',
    'VD: ["SALE50", "DISCOUNT20", "FREESHIP"]'
];

$coupon_affiliate = [
    'code' => 'Mã coupon affiliate',
    'discount' => 'Phần trăm giảm giá',
    'user_aff_id' => 'ID user affiliate',
    'expiry' => 'Thời gian hết hạn'
];

$discount = 'Phần trăm giảm giá hiện tại';
$discount_coupon = 'Số tiền được giảm';
$message_coupon = 'Thông báo về coupon (nếu có lỗi)';

// =====================================================
// 5. THÔNG TIN GIÁ CẢ
// =====================================================
$discountPrice = 'Giá sau khi giảm';
$salePrice = 'Phần trăm giảm giá';
$next_date_up_price = 'Ngày tăng giá tiếp theo (d/m/Y)';
$price_next = 'Giá sẽ tăng lên';

// =====================================================
// 6. THÔNG TIN BÀI HỌC & SECTIONS
// =====================================================
$sections = [
    'Mảng chứa các section của khóa học',
    'Mỗi section có: id, title, sort, course_id'
];

$processed_sections = 'Sections đã được xử lý với thông tin duration và video count';
$total_lesson = 'Tổng số bài học';
$enroll = 'Số lượng học viên đã đăng ký';

// =====================================================
// 7. THÔNG TIN OFFLINE PAYMENT
// =====================================================
$offline_payment = [
    'id' => 'ID thanh toán offline',
    'course_id' => 'ID khóa học',
    'user_id' => 'ID người dùng',
    'total_amount' => 'Tổng tiền cần thanh toán',
    'transaction_content' => 'Nội dung chuyển khoản',
    'status' => 'Trạng thái thanh toán (0: chờ, 1: thành công)',
    'item_type' => 'Loại item (course/bootcamp/etc)'
];

$total_amount = 'Tổng tiền cần thanh toán (từ offline_payment)';
$transaction_content = 'Nội dung chuyển khoản (từ offline_payment)';

// =====================================================
// 8. THÔNG TIN CHECKOUT RIÊNG
// =====================================================
$is_checkout = 'true nếu đang ở trang checkout';
$checkout_enabled = 'Bật/tắt chức năng checkout';
$checkout_course_offers = 'Dữ liệu ưu đãi khóa học (array)';
$checkout_video_id = 'YouTube video ID (được extract từ link)';
$checkout_page_title = 'Tiêu đề trang checkout';
$checkout_meta_description = 'Meta description cho SEO';

// =====================================================
// 9. CÁC HELPER FUNCTIONS CÓ SẴN
// =====================================================
/*
currency($amount) - Format tiền tệ
get_phrase($key) - Lấy text đa ngôn ngữ  
enroll_status($course_id, $user_id) - Kiểm tra trạng thái đăng ký
lesson_durations($lesson_id) - Lấy thời lượng bài học
getBankData($bank_code) - Lấy thông tin ngân hàng
formatCurrency($amount) - Format tiền tệ JavaScript
*/

// =====================================================
// 10. CÁC ROUTE CÓ SẴN
// =====================================================
/*
route('checkout.course') - Route xử lý checkout
route('applyCoupon') - Route áp dụng coupon
route('check.payment.success.order') - Route kiểm tra thanh toán
route('course.player', ['slug' => $slug]) - Route vào học
route('course.details', ['slug' => $slug]) - Route chi tiết khóa học
*/

// =====================================================
// 11. CÁCH SỬ DỤNG TRONG BLADE TEMPLATE
// =====================================================
?>

<!-- Hiển thị thông tin khóa học -->
<h1>{{ $course_details->title }}</h1>
<p>Giá: {{ currency($course_details->discounted_price) }}</p>

<!-- Kiểm tra có nhiều gói giá không -->
@if($course_details->hasMultiplePricingPlans())
    @php
        $plans = $course_details->getPricingPlans();
        $popularPlan = $course_details->getPopularPlan();
    @endphp
    
    @foreach($plans as $plan)
        <div class="pricing-plan">
            <h3>{{ $plan['name'] }}</h3>
            <p>{{ currency($plan['price']) }}</p>
            @if($plan['is_popular'])
                <span class="popular">Phổ biến</span>
            @endif
        </div>
    @endforeach
@endif

<!-- Hiển thị danh sách coupon -->
@if(!empty($coupons))
    <div class="coupon-list">
        @foreach($coupons as $couponCode)
            <button class="coupon-btn" data-code="{{ $couponCode }}">
                {{ $couponCode }}
            </button>
        @endforeach
    </div>
@endif

<!-- Hiển thị thông tin thanh toán -->
@if($offline_payment)
    <div class="payment-info">
        <p>Số tiền: {{ currency($offline_payment->total_amount) }}</p>
        <p>Nội dung CK: {{ $offline_payment->transaction_content }}</p>
    </div>
@endif

<!-- Hiển thị sections và lessons -->
@if($sections->count() > 0)
    @foreach($sections as $section)
        <div class="section">
            <h3>{{ $section->title }}</h3>
            @php
                $lessons = App\Models\Lesson::where('section_id', $section->id)->get();
            @endphp
            @foreach($lessons as $lesson)
                <div class="lesson">
                    <span>{{ $lesson->title }}</span>
                    <span>{{ lesson_durations($lesson->id) }}</span>
                </div>
            @endforeach
        </div>
    @endforeach
@endif

<!-- Kiểm tra trạng thái đăng ký -->
@auth
    @php
        $enrollment_status = enroll_status($course_details->id, auth()->user()->id);
    @endphp
    
    @if($enrollment_status === 'valid')
        <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}">
            Vào học ngay
        </a>
    @else
        <button>Mua khóa học</button>
    @endif
@endauth

<!-- Hiển thị thông tin checkout -->
@if($is_checkout)
    <h2>{{ $checkout_heading_1 ?? 'Tiêu đề mặc định' }}</h2>
    <p>{{ $checkout_heading_2 ?? 'Mô tả mặc định' }}</p>
    
    @if($checkout_video_id)
        <iframe src="https://www.youtube.com/embed/{{ $checkout_video_id }}"></iframe>
    @endif
    
    @if($checkout_course_offers)
        @foreach($checkout_course_offers as $offer)
            <div class="offer">
                <h4>{{ $offer['label'] ?? '' }}</h4>
                <p>{{ $offer['content'] ?? '' }}</p>
            </div>
        @endforeach
    @endif
@endif
